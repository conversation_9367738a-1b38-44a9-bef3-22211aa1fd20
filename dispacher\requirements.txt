# OptiKarburant - Fuel Distribution Optimization System
# Requirements file with all necessary dependencies

# Core Django and Python packages
Django>=4.2.0,<5.0
python-decouple>=3.8
python-dotenv>=1.0.0

# Database and GIS
psycopg2-binary>=2.9.0
GDAL>=3.4.0  # For GeoDjango

# API and Web Framework
djangorestframework>=3.14.0
django-cors-headers>=4.3.0
django-filter>=23.0
django-extensions>=3.2.0

# Background Tasks
celery>=5.3.0
redis>=4.6.0
flower>=2.0.0  # For Celery monitoring

# Route Optimization
ortools>=9.7.2996  # Google OR-Tools for VRP solving

# External Services Integration
requests>=2.31.0
urllib3>=2.0.0

# Geographic and Mapping
geopy>=2.3.0
folium>=0.14.0  # For map visualization (alternative to Leaflet)

# Date and Time
python-dateutil>=2.8.0

# Data Processing
pandas>=2.0.0  # For data analysis and reporting
numpy>=1.24.0

# Image Processing (for QR codes, charts)
Pillow>=10.0.0
matplotlib>=3.7.0
seaborn>=0.12.0

# Development and Testing
pytest>=7.4.0
pytest-django>=4.5.0
factory-boy>=3.3.0
coverage>=7.3.0

# Production and Deployment
gunicorn>=21.2.0
whitenoise>=6.5.0
sentry-sdk>=1.32.0  # Error monitoring

# Documentation
Sphinx>=7.1.0
sphinx-rtd-theme>=1.3.0

# Code Quality
black>=23.7.0
flake8>=6.0.0
isort>=5.12.0
pre-commit>=3.4.0

# Security
django-environ>=0.11.0
cryptography>=41.0.0

# Internationalization
django-modeltranslation>=0.18.0  # For model field translations

# Utilities
python-slugify>=8.0.0
django-crispy-forms>=2.0
crispy-bootstrap5>=0.7
