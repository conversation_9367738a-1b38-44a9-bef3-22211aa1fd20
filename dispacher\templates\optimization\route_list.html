<!-- templates/optimization/route_list.html - Optimized Routes List -->
{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ page_title }} - OptiKarburant{% endblock %}

{% block extra_css %}
<style>
    .route-card {
        border: none;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s;
        border-radius: 0.75rem;
        margin-bottom: 1rem;
    }
    .route-card:hover {
        transform: translateY(-2px);
    }
    .route-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    .filter-section {
        background: #f8f9fa;
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
    .route-status {
        font-size: 0.75rem;
        padding: 0.25rem 0.75rem;
        border-radius: 12px;
    }
    .efficiency-bar {
        height: 8px;
        background-color: #e9ecef;
        border-radius: 4px;
        overflow: hidden;
    }
    .efficiency-fill {
        height: 100%;
        transition: width 0.3s ease;
    }
    .metric-small {
        text-align: center;
        padding: 0.5rem;
    }
    .metric-value-small {
        font-size: 1.2rem;
        font-weight: bold;
        color: #495057;
    }
    .metric-label-small {
        font-size: 0.7rem;
        color: #6c757d;
    }
</style>
{% endblock %}

{% block page_icon %}<i class="fas fa-route text-primary"></i>{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Route Header -->
    <div class="route-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h2 class="mb-2">
                    <i class="fas fa-route me-3"></i>Optimized Routes
                </h2>
                <p class="mb-0 opacity-75">
                    AI-generated delivery routes for maximum efficiency
                </p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'optimization:form' %}" class="btn btn-light btn-lg">
                    <i class="fas fa-plus me-2"></i>New Optimization
                </a>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filter-section">
        <form method="get" class="row g-3 align-items-end">
            <div class="col-md-3">
                <label class="form-label">Date</label>
                <input type="date" name="date" class="form-control" 
                       value="{{ filters.date|default:today|date:'Y-m-d' }}">
            </div>
            <div class="col-md-2">
                <label class="form-label">Status</label>
                <select name="status" class="form-select">
                    <option value="">All Status</option>
                    <option value="draft" {% if filters.status == 'draft' %}selected{% endif %}>Draft</option>
                    <option value="active" {% if filters.status == 'active' %}selected{% endif %}>Active</option>
                    <option value="completed" {% if filters.status == 'completed' %}selected{% endif %}>Completed</option>
                    <option value="cancelled" {% if filters.status == 'cancelled' %}selected{% endif %}>Cancelled</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">Truck</label>
                <select name="truck" class="form-select">
                    <option value="">All Trucks</option>
                    {% for truck in available_trucks %}
                        <option value="{{ truck.id }}" {% if filters.truck == truck.id|stringformat:"s" %}selected{% endif %}>
                            {{ truck.emri }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-filter"></i> Filter
                </button>
                <a href="{% url 'optimization:route_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-times"></i> Clear
                </a>
            </div>
            <div class="col-md-2 text-end">
                <div class="btn-group">
                    <button class="btn btn-outline-success" onclick="exportRoutes('excel')">
                        <i class="fas fa-file-excel"></i>
                    </button>
                    <button class="btn btn-outline-danger" onclick="exportRoutes('pdf')">
                        <i class="fas fa-file-pdf"></i>
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Routes Grid -->
    <div class="row">
        {% for route in routes %}
        <div class="col-lg-6 mb-4">
            <div class="card route-card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="card-title mb-0">
                        <strong>{{ route.emri }}</strong>
                    </h6>
                    <span class="route-status bg-{{ route.status_color }}">
                        {{ route.get_statusi_display }}
                    </span>
                </div>
                <div class="card-body">
                    <!-- Route Info -->
                    <div class="row mb-3">
                        <div class="col-6">
                            <small class="text-muted">Truck:</small><br>
                            {% if route.kamion %}
                                <strong>{{ route.kamion.emri }}</strong>
                                <br><small class="text-muted">{{ route.kamion.numri_targave }}</small>
                            {% else %}
                                <span class="text-warning">Not assigned</span>
                            {% endif %}
                        </div>
                        <div class="col-6">
                            <small class="text-muted">Driver:</small><br>
                            {% if route.kamion.shofer_aktual %}
                                <strong>{{ route.kamion.shofer_aktual.emri_i_plote }}</strong>
                            {% else %}
                                <span class="text-muted">Not assigned</span>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Metrics -->
                    <div class="row mb-3">
                        <div class="col-3">
                            <div class="metric-small">
                                <div class="metric-value-small text-primary">{{ route.ndaleset.count }}</div>
                                <div class="metric-label-small">Stops</div>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="metric-small">
                                <div class="metric-value-small text-success">{{ route.total_distance_km|floatformat:1 }}</div>
                                <div class="metric-label-small">Distance (km)</div>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="metric-small">
                                <div class="metric-value-small text-info">{{ route.estimated_duration_hours|floatformat:1 }}</div>
                                <div class="metric-label-small">Duration (h)</div>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="metric-small">
                                <div class="metric-value-small text-warning">{{ route.total_fuel_liters|floatformat:0 }}</div>
                                <div class="metric-label-small">Fuel (L)</div>
                            </div>
                        </div>
                    </div>

                    <!-- Efficiency Bar -->
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-1">
                            <small class="text-muted">Route Efficiency:</small>
                            <small class="text-muted">{{ route.efficiency_percentage|floatformat:1 }}%</small>
                        </div>
                        <div class="efficiency-bar">
                            <div class="efficiency-fill bg-{{ route.efficiency_color }}" 
                                 style="width: {{ route.efficiency_percentage }}%"></div>
                        </div>
                    </div>

                    <!-- Progress (if active) -->
                    {% if route.statusi == 'active' %}
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-1">
                            <small class="text-muted">Progress:</small>
                            <small class="text-muted">{{ route.completed_stops }}/{{ route.total_stops }} stops</small>
                        </div>
                        <div class="progress" style="height: 6px;">
                            <div class="progress-bar bg-primary" 
                                 style="width: {{ route.completion_percentage }}%"></div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Optimization Details -->
                    <div class="row text-center">
                        <div class="col-6">
                            <small class="text-muted">Created:</small><br>
                            <small>{{ route.data_krijimit|date:"M d, H:i" }}</small>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">Planned Start:</small><br>
                            <small>{{ route.koha_fillimit_planifikuar|date:"H:i"|default:"Not set" }}</small>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'optimization:route_detail' route.id %}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-eye"></i> Details
                        </a>
                        
                        {% if route.statusi == 'draft' %}
                            <div>
                                <button class="btn btn-sm btn-success" onclick="activateRoute({{ route.id }})">
                                    <i class="fas fa-play"></i> Activate
                                </button>
                                <button class="btn btn-sm btn-outline-warning" onclick="editRoute({{ route.id }})">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                            </div>
                        {% elif route.statusi == 'active' %}
                            <a href="{% url 'mobile:route_detail' route.id %}" class="btn btn-sm btn-info">
                                <i class="fas fa-mobile-alt"></i> Mobile View
                            </a>
                        {% elif route.statusi == 'completed' %}
                            <div>
                                <button class="btn btn-sm btn-outline-info" onclick="viewReport({{ route.id }})">
                                    <i class="fas fa-chart-line"></i> Report
                                </button>
                                <button class="btn btn-sm btn-outline-success" onclick="reoptimizeRoute({{ route.id }})">
                                    <i class="fas fa-redo"></i> Re-optimize
                                </button>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-route fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No routes found</h5>
                <p class="text-muted">Try adjusting your filters or create a new optimization</p>
                <a href="{% url 'optimization:form' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Create New Optimization
                </a>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if routes.has_other_pages %}
    <div class="row">
        <div class="col-12">
            <nav aria-label="Route pagination">
                <ul class="pagination justify-content-center">
                    {% if routes.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ routes.previous_page_number }}">Previous</a>
                        </li>
                    {% endif %}
                    
                    {% for num in routes.paginator.page_range %}
                        {% if routes.number == num %}
                            <li class="page-item active">
                                <span class="page-link">{{ num }}</span>
                            </li>
                        {% else %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                            </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if routes.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ routes.next_page_number }}">Next</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
function activateRoute(routeId) {
    if (confirm('Activate this route? This will make it available for drivers.')) {
        fetch(`/optimization/routes/${routeId}/activate/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.error);
            }
        });
    }
}

function editRoute(routeId) {
    window.location.href = `/optimization/routes/${routeId}/edit/`;
}

function viewReport(routeId) {
    window.location.href = `/reports/routes/${routeId}/`;
}

function reoptimizeRoute(routeId) {
    if (confirm('Re-optimize this route? This will create a new optimized version.')) {
        fetch(`/optimization/routes/${routeId}/reoptimize/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Route re-optimization started!');
                location.reload();
            } else {
                alert('Error: ' + data.error);
            }
        });
    }
}

function exportRoutes(format) {
    const params = new URLSearchParams(window.location.search);
    params.set('export', format);
    window.open(`${window.location.pathname}?${params.toString()}`, '_blank');
}
</script>
{% endblock %}
