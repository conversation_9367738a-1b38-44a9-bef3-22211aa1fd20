# optimization/engine.py - Core optimization engine using Google OR-Tools

import logging
import math
from typing import List, Dict, <PERSON><PERSON>, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass

from ortools.constraint_solver import routing_enums_pb2
from ortools.constraint_solver import pywrapcp
from django.conf import settings
from django.utils import timezone

from logistics.models import (
    Porosi, Kamion, Particion, Stacion, DepoQendrore, Produkt,
    PlanRruge, Ndalese, NgarkeseShkarkese
)

logger = logging.getLogger(__name__)


@dataclass
class OptimizationRequest:
    """Request parameters for route optimization"""
    target_date: datetime.date
    available_trucks: List[int]  # Truck IDs
    open_orders: List[int]       # Order IDs
    max_route_duration_hours: float = 10.0
    optimization_timeout_seconds: int = 300


@dataclass
class OptimizationResult:
    """Result of route optimization"""
    success: bool
    message: str
    routes: List[Dict]
    total_distance_km: float
    total_duration_hours: float
    total_cost: float
    unassigned_orders: List[int]
    optimization_score: float


class DistanceMatrix:
    """Handles distance and time calculations between locations"""
    
    def __init__(self):
        self.depot_location = None
        self.station_locations = {}
        self.distance_matrix = {}
        self.time_matrix = {}
    
    def build_matrix(self, depot: DepoQendrore, stations: List[Stacion]) -> bool:
        """Build distance/time matrix between depot and stations"""
        try:
            self.depot_location = depot
            self.station_locations = {station.id: station for station in stations}
            
            # Create location list: [depot] + [stations]
            locations = [depot] + stations
            n_locations = len(locations)
            
            # Initialize matrices
            self.distance_matrix = [[0] * n_locations for _ in range(n_locations)]
            self.time_matrix = [[0] * n_locations for _ in range(n_locations)]
            
            # Calculate distances using OSRM or fallback to Haversine
            for i, loc1 in enumerate(locations):
                for j, loc2 in enumerate(locations):
                    if i == j:
                        self.distance_matrix[i][j] = 0
                        self.time_matrix[i][j] = 0
                    else:
                        distance_km, time_minutes = self._calculate_distance_time(loc1, loc2)
                        self.distance_matrix[i][j] = distance_km
                        self.time_matrix[i][j] = time_minutes
            
            return True
            
        except Exception as e:
            logger.error(f"Error building distance matrix: {e}")
            return False
    
    def _calculate_distance_time(self, loc1, loc2) -> Tuple[float, float]:
        """Calculate distance and time between two locations"""
        try:
            # Try OSRM first if configured
            osrm_url = settings.OPTIKARBURANT_SETTINGS['EXTERNAL_SERVICES']['OSRM_SERVER_URL']
            if osrm_url and settings.OPTIKARBURANT_SETTINGS['EXTERNAL_SERVICES']['USE_EXTERNAL_ROUTING']:
                return self._calculate_osrm_distance(loc1, loc2, osrm_url)
            else:
                return self._calculate_haversine_distance(loc1, loc2)
                
        except Exception as e:
            logger.warning(f"Error calculating distance, using fallback: {e}")
            return self._calculate_haversine_distance(loc1, loc2)
    
    def _calculate_osrm_distance(self, loc1, loc2, osrm_url: str) -> Tuple[float, float]:
        """Calculate distance using OSRM routing service"""
        import requests
        
        # Extract coordinates
        lon1, lat1 = loc1.vendndodhja.coords if hasattr(loc1, 'vendndodhja') else loc1.location.coords
        lon2, lat2 = loc2.vendndodhja.coords if hasattr(loc2, 'vendndodhja') else loc2.location.coords
        
        # OSRM route request
        url = f"{osrm_url}/route/v1/driving/{lon1},{lat1};{lon2},{lat2}?overview=false"
        
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        
        data = response.json()
        if data['code'] == 'Ok':
            route = data['routes'][0]
            distance_km = route['distance'] / 1000.0  # Convert meters to km
            time_minutes = route['duration'] / 60.0   # Convert seconds to minutes
            return distance_km, time_minutes
        else:
            raise Exception(f"OSRM error: {data.get('message', 'Unknown error')}")
    
    def _calculate_haversine_distance(self, loc1, loc2) -> Tuple[float, float]:
        """Calculate distance using Haversine formula (fallback)"""
        # Extract coordinates
        if hasattr(loc1, 'vendndodhja'):
            lon1, lat1 = loc1.vendndodhja.coords
        else:
            lon1, lat1 = loc1.location.coords
            
        if hasattr(loc2, 'vendndodhja'):
            lon2, lat2 = loc2.vendndodhja.coords
        else:
            lon2, lat2 = loc2.location.coords
        
        # Haversine formula
        R = 6371  # Earth's radius in km
        dlat = math.radians(lat2 - lat1)
        dlon = math.radians(lon2 - lon1)
        a = (math.sin(dlat / 2) ** 2 + 
             math.cos(math.radians(lat1)) * math.cos(math.radians(lat2)) * 
             math.sin(dlon / 2) ** 2)
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
        distance_km = R * c
        
        # Estimate time based on average speed
        avg_speed_kmh = settings.OPTIKARBURANT_SETTINGS['OPTIMIZATION']['DEFAULT_TRUCK_SPEED_KMH']
        time_minutes = (distance_km / avg_speed_kmh) * 60
        
        return distance_km, time_minutes


class VehicleRoutingOptimizer:
    """Main optimization engine using Google OR-Tools"""
    
    def __init__(self):
        self.distance_matrix = DistanceMatrix()
        self.depot_index = 0
        self.station_indices = {}
        self.orders_by_station = {}
        self.trucks_data = {}
        
    def optimize_routes(self, request: OptimizationRequest) -> OptimizationResult:
        """Main optimization method"""
        try:
            logger.info(f"Starting route optimization for {request.target_date}")
            
            # Step 1: Prepare input data
            if not self._prepare_data(request):
                return OptimizationResult(
                    success=False,
                    message="Failed to prepare optimization data",
                    routes=[], total_distance_km=0, total_duration_hours=0,
                    total_cost=0, unassigned_orders=[], optimization_score=0
                )
            
            # Step 2: Create routing model
            manager, routing = self._create_routing_model()
            if not manager or not routing:
                return OptimizationResult(
                    success=False,
                    message="Failed to create routing model",
                    routes=[], total_distance_km=0, total_duration_hours=0,
                    total_cost=0, unassigned_orders=[], optimization_score=0
                )
            
            # Step 3: Add constraints
            self._add_constraints(manager, routing, request)
            
            # Step 4: Set objective (minimize total cost)
            self._set_objective(routing)
            
            # Step 5: Solve
            search_parameters = pywrapcp.DefaultRoutingSearchParameters()
            search_parameters.first_solution_strategy = (
                routing_enums_pb2.FirstSolutionStrategy.PATH_CHEAPEST_ARC
            )
            search_parameters.local_search_metaheuristic = (
                routing_enums_pb2.LocalSearchMetaheuristic.GUIDED_LOCAL_SEARCH
            )
            search_parameters.time_limit.seconds = request.optimization_timeout_seconds
            
            logger.info("Solving optimization problem...")
            solution = routing.SolveWithParameters(search_parameters)
            
            # Step 6: Process solution
            if solution:
                return self._process_solution(manager, routing, solution, request)
            else:
                return OptimizationResult(
                    success=False,
                    message="No solution found within time limit",
                    routes=[], total_distance_km=0, total_duration_hours=0,
                    total_cost=0, unassigned_orders=request.open_orders, 
                    optimization_score=0
                )
                
        except Exception as e:
            logger.error(f"Optimization error: {e}")
            return OptimizationResult(
                success=False,
                message=f"Optimization failed: {str(e)}",
                routes=[], total_distance_km=0, total_duration_hours=0,
                total_cost=0, unassigned_orders=request.open_orders, 
                optimization_score=0
            )
    
    def _prepare_data(self, request: OptimizationRequest) -> bool:
        """Prepare all input data for optimization"""
        try:
            # Get depot
            depot = DepoQendrore.objects.filter(eshte_aktiv=True).first()
            if not depot:
                logger.error("No active depot found")
                return False
            
            # Get orders and stations
            orders = Porosi.objects.filter(
                id__in=request.open_orders,
                statusi='e_hapur'
            ).select_related('stacion', 'depozite', 'produkt')
            
            if not orders.exists():
                logger.error("No open orders found")
                return False
            
            # Group orders by station
            self.orders_by_station = {}
            stations = set()
            for order in orders:
                station_id = order.stacion.id
                if station_id not in self.orders_by_station:
                    self.orders_by_station[station_id] = []
                self.orders_by_station[station_id].append(order)
                stations.add(order.stacion)
            
            # Get available trucks
            trucks = Kamion.objects.filter(
                id__in=request.available_trucks,
                statusi='i_lire',
                eshte_aktiv=True
            ).prefetch_related('particionet')
            
            if not trucks.exists():
                logger.error("No available trucks found")
                return False
            
            # Build distance matrix
            stations_list = list(stations)
            if not self.distance_matrix.build_matrix(depot, stations_list):
                return False
            
            # Create station indices mapping
            self.station_indices = {
                station.id: idx + 1  # +1 because depot is at index 0
                for idx, station in enumerate(stations_list)
            }
            
            # Prepare truck data
            self.trucks_data = {}
            for truck in trucks:
                compartments = truck.particionet.all()
                self.trucks_data[truck.id] = {
                    'truck': truck,
                    'compartments': compartments,
                    'total_capacity': sum(c.kapaciteti for c in compartments),
                    'available_capacity': sum(c.kapaciteti_i_disponueshem for c in compartments)
                }
            
            logger.info(f"Prepared data: {len(stations)} stations, {len(orders)} orders, {len(trucks)} trucks")
            return True
            
        except Exception as e:
            logger.error(f"Error preparing data: {e}")
            return False    def _create_routing_model(self):
        """Create the routing model with managers"""
        try:
            # Number of locations (depot + stations)
            num_locations = len(self.distance_matrix.distance_matrix)
            
            # Number of vehicles (available trucks)
            num_vehicles = len(self.trucks_data)
            
            # Create routing index manager
            manager = pywrapcp.RoutingIndexManager(
                num_locations,
                num_vehicles,
                self.depot_index  # All routes start and end at depot
            )
            
            # Create routing model
            routing = pywrapcp.RoutingModel(manager)
            
            # Define distance callback
            def distance_callback(from_index, to_index):
                from_node = manager.IndexToNode(from_index)
                to_node = manager.IndexToNode(to_index)
                return int(self.distance_matrix.distance_matrix[from_node][to_node] * 1000)  # Convert to meters
            
            transit_callback_index = routing.RegisterTransitCallback(distance_callback)
            routing.SetArcCostEvaluatorOfAllVehicles(transit_callback_index)
            
            self.manager = manager
            self.routing = routing
            self.transit_callback_index = transit_callback_index
            
            return manager, routing
            
        except Exception as e:
            logger.error(f"Error creating routing model: {e}")
            return None, None
    
    def _add_constraints(self, manager, routing, request: OptimizationRequest):
        """Add all constraints to the routing model"""
        
        # 1. Time dimension constraint
        self._add_time_constraints(manager, routing, request)
        
        # 2. Capacity constraints for each product
        self._add_capacity_constraints(manager, routing)
        
        # 3. Equipment compatibility constraints
        self._add_equipment_constraints(manager, routing)
        
        # 4. Delivery time windows
        self._add_time_window_constraints(manager, routing)
    
    def _add_time_constraints(self, manager, routing, request: OptimizationRequest):
        """Add time-based constraints"""
        
        def time_callback(from_index, to_index):
            from_node = manager.IndexToNode(from_index)
            to_node = manager.IndexToNode(to_index)
            
            # Travel time
            travel_time = int(self.distance_matrix.time_matrix[from_node][to_node])
            
            # Service time at destination (if not depot)
            service_time = 0
            if to_node != self.depot_index:
                service_time = settings.OPTIKARBURANT_SETTINGS['BUSINESS_RULES']['DEFAULT_SERVICE_TIME_MINUTES']
            
            return travel_time + service_time
        
        time_callback_index = routing.RegisterTransitCallback(time_callback)
        
        # Create time dimension
        max_time = int(request.max_route_duration_hours * 60)  # Convert to minutes
        
        routing.AddDimension(
            time_callback_index,
            max_time,  # Maximum slack time
            max_time,  # Maximum total time per vehicle
            False,     # Don't force start cumul to zero
            'Time'
        )
        
        time_dimension = routing.GetDimensionOrDie('Time')
        
        # Set maximum working hours per truck
        for vehicle_id in range(len(self.trucks_data)):
            time_dimension.SetCumulVarMax(
                manager.End(vehicle_id),
                max_time
            )
    
    def _add_capacity_constraints(self, manager, routing):
        """Add capacity constraints for each product type"""
        
        # Get all products involved in orders
        products = set()
        for orders_list in self.orders_by_station.values():
            for order in orders_list:
                products.add(order.produkt)
        
        # Create capacity dimension for each product
        for product in products:
            def make_demand_callback(product_obj):
                def demand_callback(from_index):
                    from_node = manager.IndexToNode(from_index)
                    
                    if from_node == self.depot_index:
                        return 0
                    
                    # Find station and calculate demand for this product
                    station_id = self._get_station_id_by_index(from_node)
                    if station_id in self.orders_by_station:
                        total_demand = 0
                        for order in self.orders_by_station[station_id]:
                            if order.produkt == product_obj:
                                total_demand += int(order.sasia_e_miratuar)
                        return total_demand
                    return 0
                return demand_callback
            
            demand_callback = make_demand_callback(product)
            demand_callback_index = routing.RegisterUnaryTransitCallback(demand_callback)
            
            # Add capacity dimension
            routing.AddDimensionWithVehicleCapacity(
                demand_callback_index,
                0,  # No slack
                [int(self._get_truck_capacity_for_product(truck_id, product)) 
                 for truck_id in self.trucks_data.keys()],  # Vehicle capacities
                True,  # Start cumul to zero
                f'Capacity_{product.id}'
            )
    
    def _add_equipment_constraints(self, manager, routing):
        """Add equipment compatibility constraints"""
        
        for station_id, orders_list in self.orders_by_station.items():
            station = orders_list[0].stacion  # Get station from first order
            station_index = self.station_indices[station_id]
            
            # Check equipment requirements
            requires_pump = station.kerkon_pompe
            requires_flow_meter = station.kerkon_kontaliter
            
            if requires_pump or requires_flow_meter:
                # Create list of compatible vehicles
                compatible_vehicles = []
                for i, (truck_id, truck_data) in enumerate(self.trucks_data.items()):
                    truck = truck_data['truck']
                    
                    is_compatible = True
                    if requires_pump and not truck.ka_pompe:
                        is_compatible = False
                    if requires_flow_meter and not truck.ka_kontaliter:
                        is_compatible = False
                    
                    if is_compatible:
                        compatible_vehicles.append(i)
                
                # If no compatible vehicles, this is a problem
                if not compatible_vehicles:
                    logger.warning(f"No compatible trucks for station {station.emri}")
                    continue
                
                # Add constraint: only compatible vehicles can visit this station
                for vehicle_id in range(len(self.trucks_data)):
                    if vehicle_id not in compatible_vehicles:
                        routing.VehicleVar(manager.NodeToIndex(station_index)).SetValue(vehicle_id)
    
    def _add_time_window_constraints(self, manager, routing):
        """Add delivery time window constraints"""
        
        time_dimension = routing.GetDimensionOrDie('Time')
        
        for station_id, orders_list in self.orders_by_station.items():
            station = orders_list[0].stacion
            station_index = self.station_indices[station_id]
            node_index = manager.NodeToIndex(station_index)
            
            # Convert station operating hours to minutes from start of route
            # Assuming routes start at 6:00 AM
            route_start_hour = 6
            
            start_minutes = (station.orar_pranimi_nga.hour - route_start_hour) * 60 + station.orar_pranimi_nga.minute
            end_minutes = (station.orar_pranimi_deri.hour - route_start_hour) * 60 + station.orar_pranimi_deri.minute
            
            # Ensure positive values
            start_minutes = max(0, start_minutes)
            end_minutes = max(start_minutes + 60, end_minutes)  # At least 1 hour window
            
            time_dimension.CumulVar(node_index).SetRange(start_minutes, end_minutes)
    
    def _set_objective(self, routing):
        """Set optimization objective to minimize total cost"""
        # The objective is already set to minimize the arc costs (distance)
        # We could add penalty costs for undelivered orders here
        
        # Add penalty for unvisited nodes (undelivered orders)
        penalty = 1000000  # Large penalty
        for station_id in self.station_indices:
            station_index = self.station_indices[station_id]
            routing.AddDisjunction([self.manager.NodeToIndex(station_index)], penalty)
    
    def _process_solution(self, manager, routing, solution, request: OptimizationRequest) -> OptimizationResult:
        """Process the optimization solution and create route plans"""
        
        routes = []
        total_distance_km = 0
        total_duration_hours = 0
        total_cost = 0
        assigned_orders = set()
        
        # Extract routes for each vehicle
        for vehicle_id in range(len(self.trucks_data)):
            truck_id = list(self.trucks_data.keys())[vehicle_id]
            truck_data = self.trucks_data[truck_id]
            
            route_distance = 0
            route_duration = 0
            route_stops = []
            
            index = routing.Start(vehicle_id)
            
            while not routing.IsEnd(index):
                node_index = manager.IndexToNode(index)
                
                if node_index != self.depot_index:  # Not depot
                    # Find station
                    station_id = self._get_station_id_by_index(node_index)
                    if station_id and station_id in self.orders_by_station:
                        station_orders = self.orders_by_station[station_id]
                        
                        # Calculate deliveries for this stop
                        deliveries = self._calculate_deliveries(
                            truck_data, station_orders, vehicle_id
                        )
                        
                        if deliveries:
                            route_stops.append({
                                'station_id': station_id,
                                'orders': [order.id for order in station_orders],
                                'deliveries': deliveries
                            })
                            
                            # Mark orders as assigned
                            for order in station_orders:
                                assigned_orders.add(order.id)
                
                # Move to next node
                previous_index = index
                index = solution.Value(routing.NextVar(index))
                
                # Add distance and time
                if not routing.IsEnd(index):
                    route_distance += self.distance_matrix.distance_matrix[
                        manager.IndexToNode(previous_index)
                    ][manager.IndexToNode(index)]
                    
                    route_duration += self.distance_matrix.time_matrix[
                        manager.IndexToNode(previous_index)
                    ][manager.IndexToNode(index)]
            
            # Only add route if it has stops
            if route_stops:
                routes.append({
                    'truck_id': truck_id,
                    'distance_km': route_distance,
                    'duration_minutes': route_duration,
                    'stops': route_stops
                })
                
                total_distance_km += route_distance
                total_duration_hours += route_duration / 60.0
        
        # Calculate cost (simplified: distance * fuel consumption)
        avg_fuel_consumption = 35  # L/100km
        fuel_price_per_liter = 1.5  # EUR per liter
        total_cost = total_distance_km * (avg_fuel_consumption / 100) * fuel_price_per_liter
        
        # Find unassigned orders
        unassigned_orders = [
            order_id for order_id in request.open_orders 
            if order_id not in assigned_orders
        ]
        
        # Calculate optimization score (lower is better)
        optimization_score = solution.ObjectiveValue() / 1000000  # Convert from penalty units
        
        logger.info(f"Optimization completed: {len(routes)} routes, "
                   f"{len(assigned_orders)} orders assigned, "
                   f"{len(unassigned_orders)} unassigned")
        
        return OptimizationResult(
            success=True,
            message=f"Successfully created {len(routes)} routes",
            routes=routes,
            total_distance_km=total_distance_km,
            total_duration_hours=total_duration_hours,
            total_cost=total_cost,
            unassigned_orders=unassigned_orders,
            optimization_score=optimization_score
        )
    
    def _get_station_id_by_index(self, node_index: int) -> Optional[int]:
        """Get station ID from node index"""
        for station_id, index in self.station_indices.items():
            if index == node_index:
                return station_id
        return None
    
    def _get_truck_capacity_for_product(self, truck_id: int, product: Produkt) -> float:
        """Get truck's capacity for a specific product"""
        truck_data = self.trucks_data[truck_id]
        total_capacity = 0
        
        for compartment in truck_data['compartments']:
            if compartment.mund_te_ngarkohet_produkti(product):
                total_capacity += compartment.kapaciteti_i_disponueshem
        
        return total_capacity
    
    def _calculate_deliveries(self, truck_data: Dict, orders: List, vehicle_id: int) -> List[Dict]:
        """Calculate optimal deliveries for a truck at a station"""
        deliveries = []
        
        # Group orders by product
        orders_by_product = {}
        for order in orders:
            product = order.produkt
            if product not in orders_by_product:
                orders_by_product[product] = []
            orders_by_product[product].append(order)
        
        # Assign deliveries to compartments
        for product, product_orders in orders_by_product.items():
            total_demand = sum(order.sasia_e_miratuar for order in product_orders)
            remaining_demand = total_demand
            
            # Find suitable compartments
            suitable_compartments = [
                comp for comp in truck_data['compartments']
                if comp.mund_te_ngarkohet_produkti(product)
            ]
            
            for compartment in suitable_compartments:
                if remaining_demand <= 0:
                    break
                
                available_capacity = compartment.kapaciteti_i_disponueshem
                delivery_quantity = min(remaining_demand, available_capacity)
                
                if delivery_quantity > 0:
                    deliveries.append({
                        'compartment_id': compartment.id,
                        'product_id': product.id,
                        'quantity': delivery_quantity,
                        'orders': [order.id for order in product_orders]
                    })
                    
                    remaining_demand -= delivery_quantity
            
            if remaining_demand > 0:
                logger.warning(f"Could not assign {remaining_demand}L of {product.emri} "
                              f"for truck {truck_data['truck'].targa}")
        
        return deliveries


def run_optimization(request: OptimizationRequest) -> OptimizationResult:
    """Main function to run route optimization"""
    optimizer = VehicleRoutingOptimizer()
    return optimizer.optimize_routes(request)
