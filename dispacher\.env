# .env.example - Environment variables template for OptiKarburant

# Django Settings
DEBUG=True
SECRET_KEY=django-insecure-optikarburant-change-this-in-production
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0

# Database Configuration
DB_NAME=optikarburant
DB_USER=postgres
DB_PASSWORD=postgres
DB_HOST=localhost
DB_PORT=5432

# Redis Configuration (for Celery)
REDIS_URL=redis://localhost:6379/0

# External Services
OSRM_URL=http://localhost:5000
NOMINATIM_URL=http://localhost:8080
USE_EXTERNAL_ROUTING=true

# Google Maps API (optional)
GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=true
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your_app_password
DEFAULT_FROM_EMAIL=<EMAIL>

# Sentry Error Monitoring (optional)
SENTRY_DSN=your_sentry_dsn_here

# File Storage
MEDIA_ROOT=/path/to/media
STATIC_ROOT=/path/to/static

# Optimization Settings
MAX_ROUTE_DURATION_HOURS=10
MAX_WORKING_HOURS_PER_DAY=8
OPTIMIZATION_TIMEOUT_SECONDS=300

# Security Settings (for production)
SECURE_SSL_REDIRECT=False
SECURE_HSTS_SECONDS=0
SECURE_HSTS_INCLUDE_SUBDOMAINS=False
SECURE_CONTENT_TYPE_NOSNIFF=True
SECURE_BROWSER_XSS_FILTER=True
SESSION_COOKIE_SECURE=False
CSRF_COOKIE_SECURE=False
