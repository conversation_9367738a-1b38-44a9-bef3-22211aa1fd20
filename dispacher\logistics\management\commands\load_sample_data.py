# logistics/management/commands/load_sample_data.py
"""
Management command to load sample data for OptiKarburant system
"""

from django.core.management.base import BaseCommand
from django.contrib.gis.geos import Point
from django.utils import timezone
from datetime import time, timedelta

from logistics.models import (
    Produkt, DepoQendrore, Stacion, De<PERSON>, <PERSON>, 
    Kamion, Particion, Porosi
)


class Command(BaseCommand):
    help = 'Load sample data for OptiKarburant system'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing data before loading',
        )
    
    def handle(self, *args, **options):
        if options['clear']:
            self.stdout.write('Clearing existing data...')
            self.clear_data()
        
        self.stdout.write('Loading sample data...')
        
        # Load products
        products = self.create_products()
        self.stdout.write(f'Created {len(products)} products')
        
        # Load central depot
        depot = self.create_central_depot()
        self.stdout.write(f'Created central depot: {depot.emri}')
        
        # Load stations
        stations = self.create_stations()
        self.stdout.write(f'Created {len(stations)} stations')
        
        # Load tanks
        tanks = self.create_tanks(stations, products)
        self.stdout.write(f'Created {len(tanks)} tanks')
        
        # Load drivers
        drivers = self.create_drivers()
        self.stdout.write(f'Created {len(drivers)} drivers')
        
        # Load trucks
        trucks = self.create_trucks(drivers)
        self.stdout.write(f'Created {len(trucks)} trucks')
        
        # Load compartments
        compartments = self.create_compartments(trucks, products)
        self.stdout.write(f'Created {len(compartments)} compartments')
        
        # Load sample orders
        orders = self.create_sample_orders(stations, tanks, products)
        self.stdout.write(f'Created {len(orders)} sample orders')
        
        self.stdout.write(
            self.style.SUCCESS('Successfully loaded sample data!')
        )
    
    def clear_data(self):
        """Clear existing data"""
        Porosi.objects.all().delete()
        Particion.objects.all().delete()
        Kamion.objects.all().delete()
        Shofer.objects.all().delete()
        Depozite.objects.all().delete()
        Stacion.objects.all().delete()
        DepoQendrore.objects.all().delete()
        Produkt.objects.all().delete()
    
    def create_products(self):
        """Create sample fuel products"""
        products_data = [
            {'emri': 'Naftë D2', 'densiteti': 0.85, 'ngjyra_kodi': '#2c3e50'},
            {'emri': 'Benzinë 95', 'densiteti': 0.75, 'ngjyra_kodi': '#e74c3c'},
            {'emri': 'Benzinë 100', 'densiteti': 0.75, 'ngjyra_kodi': '#c0392b'},
            {'emri': 'Gaz i Lëngshëm', 'densiteti': 0.51, 'ngjyra_kodi': '#3498db'},
            {'emri': 'Naftë Heating', 'densiteti': 0.87, 'ngjyra_kodi': '#8e44ad'},
        ]
        
        products = []
        for data in products_data:
            product, created = Produkt.objects.get_or_create(
                emri=data['emri'],
                defaults=data
            )
            products.append(product)
        
        # Set product compatibility
        nafte = products[0]  # Naftë D2
        benzine_95 = products[1]  # Benzinë 95
        benzine_100 = products[2]  # Benzinë 100
        
        # Benzines are compatible with each other
        benzine_95.produkte_kompatible.add(benzine_100)
        benzine_100.produkte_kompatible.add(benzine_95)
        
        return products
    
    def create_central_depot(self):
        """Create central depot in Tirana"""
        depot, created = DepoQendrore.objects.get_or_create(
            emri='Depo Qendrore Tiranë',
            defaults={
                'vendndodhja': Point(19.8189, 41.3275),  # Tirana coordinates
                'adresa': 'Rruga Industriale, Tiranë',
                'kapaciteti_ngarkimi': 8,
                'koha_mesatare_ngarkimi': 90,
                'orar_punes_nga': time(6, 0),
                'orar_punes_deri': time(22, 0),
            }
        )
        return depot
    
    def create_stations(self):
        """Create sample fuel stations around Albania"""
        stations_data = [
            # Tirana area
            {'emri': 'Stacioni Qender', 'kodi': 'TIR001', 'lat': 41.3275, 'lng': 19.8189, 'city': 'Tiranë'},
            {'emri': 'Stacioni Kombinat', 'kodi': 'TIR002', 'lat': 41.2911, 'lng': 19.8607, 'city': 'Tiranë'},
            {'emri': 'Stacioni Don Bosko', 'kodi': 'TIR003', 'lat': 41.3151, 'lng': 19.8331, 'city': 'Tiranë'},
            
            # Durrës
            {'emri': 'Stacioni Durrës Port', 'kodi': 'DUR001', 'lat': 41.3147, 'lng': 19.4444, 'city': 'Durrës'},
            {'emri': 'Stacioni Durrës Qender', 'kodi': 'DUR002', 'lat': 41.3236, 'lng': 19.4581, 'city': 'Durrës'},
            
            # Shkodër
            {'emri': 'Stacioni Shkodër', 'kodi': 'SHK001', 'lat': 42.0683, 'lng': 19.5122, 'city': 'Shkodër'},
            
            # Elbasan
            {'emri': 'Stacioni Elbasan', 'kodi': 'ELB001', 'lat': 41.1125, 'lng': 20.0822, 'city': 'Elbasan'},
            
            # Vlorë
            {'emri': 'Stacioni Vlorë', 'kodi': 'VLO001', 'lat': 40.4686, 'lng': 19.4889, 'city': 'Vlorë'},
            
            # Korçë
            {'emri': 'Stacioni Korçë', 'kodi': 'KOR001', 'lat': 40.6186, 'lng': 20.7719, 'city': 'Korçë'},
            
            # Fier
            {'emri': 'Stacioni Fier', 'kodi': 'FIE001', 'lat': 40.7239, 'lng': 19.5556, 'city': 'Fier'},
        ]
        
        stations = []
        for data in stations_data:
            station, created = Stacion.objects.get_or_create(
                kodi=data['kodi'],
                defaults={
                    'emri': data['emri'],
                    'vendndodhja': Point(data['lng'], data['lat']),
                    'adresa': f"{data['emri']}, {data['city']}, Shqipëri",
                    'orar_pranimi_nga': time(6, 0),
                    'orar_pranimi_deri': time(18, 0),
                    'kerkon_pompe': data['kodi'].endswith('001'),  # First station in each city requires pump
                    'kerkon_kontaliter': True,
                    'max_kamione_njekohesisht': 1,
                    'koha_mesatare_shkarkimi': 45,
                }
            )
            stations.append(station)
        
        return stations
    
    def create_tanks(self, stations, products):
        """Create sample tanks for stations"""
        tanks = []
        
        for station in stations:
            # Each station gets 2-4 tanks with different products
            for i, product in enumerate(products[:3]):  # First 3 products
                if i == 2 and station.kodi.endswith('002'):
                    continue  # Smaller stations don't have all products
                
                tank, created = Depozite.objects.get_or_create(
                    stacion=station,
                    produkt=product,
                    numri_tankut=f"T{i+1}",
                    defaults={
                        'kapaciteti_total': 20000 + (i * 5000),  # 20k, 25k, 30k
                        'sasia_aktuale': 15000 + (i * 3000),     # Current level
                        'niveli_minimal_sigurise': 2000,
                        'niveli_i_porosise': 5000,
                        'konsumi_mesatar_ditor': 800 + (i * 200),
                        'sasia_minimale_dorezimi': 3000,
                        'perqindja_maksimale_mbushjes': 95,
                    }
                )
                tanks.append(tank)
        
        return tanks
    
    def create_drivers(self):
        """Create sample drivers"""
        drivers_data = [
            {'emri': 'Agim', 'mbiemri': 'Kelmendi', 'telefoni': '+355691234567'},
            {'emri': 'Besnik', 'mbiemri': 'Hoxha', 'telefoni': '+355692345678'},
            {'emri': 'Driton', 'mbiemri': 'Brahimaj', 'telefoni': '+355693456789'},
            {'emri': 'Ermal', 'mbiemri': 'Gjoka', 'telefoni': '+355694567890'},
            {'emri': 'Fadil', 'mbiemri': 'Rama', 'telefoni': '+355695678901'},
        ]
        
        drivers = []
        for i, data in enumerate(drivers_data):
            driver, created = Shofer.objects.get_or_create(
                leje_drejtimi_numri=f"DL{2024000 + i}",
                defaults={
                    'emri': data['emri'],
                    'mbiemri': data['mbiemri'],
                    'telefoni': data['telefoni'],
                    'email': f"{data['emri'].lower()}.{data['mbiemri'].lower()}@optikarburant.com",
                    'leje_drejtimi_skadon': timezone.now().date() + timedelta(days=365),
                    'leje_adr': i < 3,  # First 3 drivers have ADR license
                    'ore_punes_maksimale_ditor': 8,
                    'ore_drejtimi_maksimale_ditor': 6,
                    'data_punesimit': timezone.now().date() - timedelta(days=30 * (i + 1)),
                }
            )
            drivers.append(driver)
        
        return drivers
    
    def create_trucks(self, drivers):
        """Create sample trucks"""
        trucks_data = [
            {'targa': 'TR 001 AA', 'modeli': 'Mercedes Actros', 'has_pump': True, 'has_meter': True},
            {'targa': 'TR 002 AA', 'modeli': 'Volvo FH', 'has_pump': True, 'has_meter': True},
            {'targa': 'TR 003 AA', 'modeli': 'Scania R-Series', 'has_pump': False, 'has_meter': True},
            {'targa': 'TR 004 AA', 'modeli': 'MAN TGX', 'has_pump': True, 'has_meter': True},
            {'targa': 'TR 005 AA', 'modeli': 'DAF XF', 'has_pump': False, 'has_meter': False},
        ]
        
        trucks = []
        for i, data in enumerate(trucks_data):
            driver = drivers[i] if i < len(drivers) else None
            
            truck, created = Kamion.objects.get_or_create(
                targa=data['targa'],
                defaults={
                    'modeli': data['modeli'],
                    'viti_prodhimit': 2020 + (i % 4),
                    'shofer_aktual': driver,
                    'pesha_maksimale_bruto_ton': 40.0,
                    'gjatesia_totale_m': 16.5,
                    'eshte_trailer': True,
                    'ka_pompe': data['has_pump'],
                    'ka_kontaliter': data['has_meter'],
                    'ka_gps': True,
                    'statusi': 'i_lire',
                    'odometri_aktual_km': 150000 + (i * 20000),
                    'km_mirembajtjes_rradheses': 200000 + (i * 20000),
                    'konsumi_mesatar_l_100km': 35.0,
                }
            )
            trucks.append(truck)
        
        return trucks
    
    def create_compartments(self, trucks, products):
        """Create compartments for trucks"""
        compartments = []
        
        for truck in trucks:
            # Each truck has 3-5 compartments
            num_compartments = 4 if truck.targa.endswith('AA') else 3
            
            for i in range(num_compartments):
                compartment, created = Particion.objects.get_or_create(
                    kamion=truck,
                    numri_i_dhomes=i + 1,
                    defaults={
                        'kapaciteti': 8000 if i < 2 else 6000,  # Larger front compartments
                        'produkt_i_dedikuar': products[i] if i < len(products) else None,
                        'sasia_heel_litra': 50,
                        'eshte_i_pastruar': True,
                        'sasia_aktuale': 0,
                    }
                )
                compartments.append(compartment)
        
        return compartments
    
    def create_sample_orders(self, stations, tanks, products):
        """Create sample orders for testing"""
        orders = []
        
        # Create orders for tanks that need refill
        low_tanks = [tank for tank in tanks if tank.nevojitet_rifornizim]
        
        for tank in low_tanks[:5]:  # Create orders for first 5 low tanks
            order, created = Porosi.objects.get_or_create(
                stacion=tank.stacion,
                depozite=tank,
                produkt=tank.produkt,
                defaults={
                    'sasia_e_kerkuar': tank.kapaciteti_i_disponueshem * 0.8,  # Fill to 80%
                    'prioriteti': 'e_larte' if tank.eshte_kritik else 'normale',
                    'data_afati': timezone.now() + timedelta(days=1),
                    'eshte_automatike': True,
                    'shenimet': 'Sample order created by load_sample_data command',
                }
            )
            orders.append(order)
        
        return orders
